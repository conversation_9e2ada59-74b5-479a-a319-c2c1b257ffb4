! generic parameters
@SET LAMBDA 0.0
@SET a 0

&<PERSON><PERSON><PERSON>BAL
  PROJECT sb(oh)3(oh2)-${LAMBDA}
  RUN_TYPE MD 
  PRINT_LEVEL low
  FLUSH_SHOULD_FLUSH T
  WALLTIME 80500000
&END GLOBAL

&MULTIPLE_FORCE_EVALS
  FORCE_EVAL_ORDER 1 2 3  
  MULTIPLE_SUBSYS T
&END MULTIPLE_FORCE_EVALS

@SET METHOD MIXED
@include input.inc

! force_eva for oh3
@SET METHOD QS
@SET a 1
@include input.inc

! force_eva for oh2d
@SET METHOD QS
@SET a 2
@include input.inc

&MOTION

  &CONSTRAINT

      &COLLECTIVE
       TARGET [angstrom] 1.0     
       INTERMOLECULAR
       COLVAR 1
       &RESTRAINT
        k 0.1
       &END RESTRAINT
     &END COLLECTIVE  

     &COLLECTIVE
       TARGET [deg] 103   
       INTERMOLECULAR
       COLVAR 2
       &RESTRAINT
        k 0.1 
       &<PERSON><PERSON> RESTRAINT
     &END COLLECTIVE 

     &COLLECTIVE
       TARGET [deg] 120   
       INTERMOLECULAR
       COLVAR 3
       &RESTRAINT
        k 0.1 
       &END RESTRAINT
     &END COLLECTIVE 

     @include COLVAR.inc   

  &END CONSTRAINT

  &MD
    COMVEL_TOL 2.0E-6     
    &THERMOSTAT
      &NOSE
        LENGTH 3
        YOSHIDA 3
        TIMECON 1000.
        MTS 2
      &END NOSE
    &END THERMOSTAT
    ENSEMBLE NVT
    STEPS 4000
    TIMESTEP 0.5
    TEMPERATURE 330.0
    TEMP_TOL 50.0
  &END MD

  &PRINT
   &TRAJECTORY
     &EACH
       MD 10
     &END EACH
   &END TRAJECTORY
   &RESTART_HISTORY
     &EACH
       MD 1000
     &END EACH
   &END RESTART_HISTORY
   &RESTART
     BACKUP_COPIES 2
     &EACH
       MD 1
     &END EACH
   &END RESTART
  &END PRINT

&END MOTION 
 
&EXT_RESTART
  RESTART_FILE_NAME sb(oh)3(oh2)-0.0-1.restart
#  RESTART_COUNTERS False
&END EXT_RESTART



